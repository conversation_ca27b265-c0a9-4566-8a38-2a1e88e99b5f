package com.zhisuo.manager.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssConfig {
    
    /**
     * OSS服务端点
     */
    private String endpoint;
    
    /**
     * 访问密钥ID
     */
    private String accessKeyId;
    
    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;
    
    /**
     * 存储桶名称
     */
    private String bucketName;
    
    /**
     * 自定义域名（可选）
     */
    private String customDomain;
    
    /**
     * 是否使用HTTPS
     */
    private Boolean useHttps = true;
    
    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectionTimeout = 10000;
    
    /**
     * Socket超时时间（毫秒）
     */
    private Integer socketTimeout = 10000;
    
    /**
     * 最大连接数
     */
    private Integer maxConnections = 1024;
    
    /**
     * 获取完整的访问URL
     * @param objectName 对象名称
     * @return 完整的访问URL
     */
    public String getFullUrl(String objectName) {
        if (customDomain != null && !customDomain.isEmpty()) {
            return (useHttps ? "https://" : "http://") + customDomain + "/" + objectName;
        } else {
            return (useHttps ? "https://" : "http://") + bucketName + "." + endpoint + "/" + objectName;
        }
    }
}
