package com.zhisuo.manager.controller;

import com.zhisuo.manager.common.Result;
import com.zhisuo.manager.util.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/upload")
public class UploadController {

    @Autowired
    private OssUtil ossUtil;

    /**
     * 上传头像
     * @param file 头像文件
     * @return 返回上传结果
     */
    @PostMapping("/avatar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file) {
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                return Result.error("上传文件不能为空");
            }
            
            // 检查文件类型
            String contentType = file.getContentType();
            if (!ossUtil.isImageFile(contentType)) {
                return Result.error("上传文件类型错误，仅支持图片格式");
            }
            
            // 检查文件大小，限制为2MB
            long maxSize = 2 * 1024 * 1024; // 2MB
            if (!ossUtil.isValidFileSize(file.getSize(), maxSize)) {
                return Result.error("上传图片大小不能超过2MB");
            }
            
            // 上传到OSS，保存在image/avatar/目录下
            String avatarUrl = ossUtil.uploadFile(file, "image/avatar/");
            
            // 返回上传结果
            Map<String, String> data = new HashMap<>();
            data.put("url", avatarUrl);
            data.put("originalName", file.getOriginalFilename());
            data.put("size", String.valueOf(file.getSize()));
            
            log.info("头像上传成功: url={}, originalName={}, size={}", 
                    avatarUrl, file.getOriginalFilename(), file.getSize());
            
            return Result.success("头像上传成功", data);
            
        } catch (IOException e) {
            log.error("头像上传失败", e);
            return Result.error("上传头像失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("头像上传异常", e);
            return Result.error("上传头像失败，请稍后重试");
        }
    }

    /**
     * 上传文件（通用）
     * @param file 文件
     * @param type 文件类型（可选，用于分类存储）
     * @return 返回上传结果
     */
    @PostMapping("/file")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public Result<Map<String, String>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", defaultValue = "file") String type) {
        try {
            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                return Result.error("上传文件不能为空");
            }
            
            // 检查文件大小，限制为10MB
            long maxSize = 10 * 1024 * 1024; // 10MB
            if (!ossUtil.isValidFileSize(file.getSize(), maxSize)) {
                return Result.error("上传文件大小不能超过10MB");
            }
            
            // 根据类型确定存储目录
            String dir = getDirectoryByType(type);
            
            // 上传到OSS
            String fileUrl = ossUtil.uploadFile(file, dir);
            
            // 返回上传结果
            Map<String, String> data = new HashMap<>();
            data.put("url", fileUrl);
            data.put("originalName", file.getOriginalFilename());
            data.put("size", String.valueOf(file.getSize()));
            data.put("type", type);
            
            log.info("文件上传成功: url={}, originalName={}, size={}, type={}", 
                    fileUrl, file.getOriginalFilename(), file.getSize(), type);
            
            return Result.success("文件上传成功", data);
            
        } catch (IOException e) {
            log.error("文件上传失败", e);
            return Result.error("上传文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("文件上传异常", e);
            return Result.error("上传文件失败，请稍后重试");
        }
    }

    /**
     * 删除文件
     * @param fileUrl 文件URL
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deleteFile(@RequestBody Map<String, String> request) {
        try {
            String fileUrl = request.get("fileUrl");
            if (fileUrl == null || fileUrl.isEmpty()) {
                return Result.error("文件URL不能为空");
            }
            
            // 从URL中提取对象名称
            String objectName = extractObjectNameFromUrl(fileUrl);
            if (objectName == null) {
                return Result.error("无效的文件URL");
            }
            
            // 删除文件
            boolean success = ossUtil.deleteFile(objectName);
            if (success) {
                log.info("文件删除成功: objectName={}", objectName);
                return Result.success("文件删除成功", null);
            } else {
                return Result.error("文件删除失败");
            }
            
        } catch (Exception e) {
            log.error("文件删除异常", e);
            return Result.error("文件删除失败，请稍后重试");
        }
    }

    /**
     * 根据文件类型获取存储目录
     * @param type 文件类型
     * @return 存储目录
     */
    private String getDirectoryByType(String type) {
        switch (type.toLowerCase()) {
            case "avatar":
                return "image/avatar/";
            case "image":
                return "image/";
            case "document":
                return "document/";
            case "video":
                return "video/";
            case "audio":
                return "audio/";
            default:
                return "file/";
        }
    }

    /**
     * 从URL中提取对象名称
     * @param fileUrl 文件URL
     * @return 对象名称
     */
    private String extractObjectNameFromUrl(String fileUrl) {
        try {
            // 假设URL格式为: https://bucket.endpoint/objectName
            // 或者: https://customDomain/objectName
            int lastSlashIndex = fileUrl.lastIndexOf('/');
            if (lastSlashIndex != -1 && lastSlashIndex < fileUrl.length() - 1) {
                return fileUrl.substring(lastSlashIndex + 1);
            }
            return null;
        } catch (Exception e) {
            log.error("提取对象名称失败: fileUrl={}", fileUrl, e);
            return null;
        }
    }
}
