package com.zhisuo.manager.dto;

import lombok.Data;

/**
 * 用户完整档案DTO（包含基本信息和统计数据）
 */
@Data
public class UserProfileDTO {
    
    // ========== 基本信息 ==========
    
    /**
     * 管理员ID
     */
    private String adminId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 角色
     */
    private String role;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    // ========== 统计数据 ==========
    
    /**
     * 用户统计数据
     */
    private UserStatsDTO stats;
}
