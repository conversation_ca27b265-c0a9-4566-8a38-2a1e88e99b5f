package com.zhisuo.manager.dto;

import lombok.Data;

/**
 * 用户统计数据DTO
 */
@Data
public class UserStatsDTO {
    
    /**
     * 最后登录时间
     */
    private Long lastLoginTime;
    
    /**
     * 最后活跃时间
     */
    private Long lastActiveTime;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    /**
     * 是否在线
     */
    private Boolean isOnline;
    
    /**
     * 登录次数
     */
    private Integer loginCount;
    
    /**
     * 账号创建时间
     */
    private Long createTime;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 地理位置
     */
    private String location;
}
