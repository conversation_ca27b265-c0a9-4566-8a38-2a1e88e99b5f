package com.zhisuo.manager.security;

import cn.hutool.core.util.StrUtil;
import com.zhisuo.manager.common.JwtUtil;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private AuthService authService;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Value("${jwt.header}")
    private String tokenHeader;
    
    @Value("${jwt.prefix}")
    private String tokenPrefix;
    
    private static final String TOKEN_PREFIX = "manager:token:";
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        
        String authHeader = request.getHeader(tokenHeader);
        
        if (StrUtil.isNotBlank(authHeader) && authHeader.startsWith(tokenPrefix)) {
            String token = authHeader.substring(tokenPrefix.length()).trim();
            
            try {
                // 从Token中获取用户信息
                String username = jwtUtil.getUsernameFromToken(token);
                String userId = jwtUtil.getUserIdFromToken(token);
                
                if (StrUtil.isNotBlank(username) && SecurityContextHolder.getContext().getAuthentication() == null) {
                    // 验证Token是否在Redis中存在
                    String tokenKey = TOKEN_PREFIX + userId;
                    String redisToken = redisTemplate.opsForValue().get(tokenKey);
                    
                    if (StrUtil.isNotBlank(redisToken) && redisToken.equals(token)) {
                        // 验证Token是否有效
                        if (jwtUtil.validateToken(token, username)) {
                            // 获取管理员信息
                            Admin admin = authService.getAdminById(userId);
                            if (admin != null && admin.getStatus() == 1) {
                                // 创建认证对象
                                UsernamePasswordAuthenticationToken authentication =
                                    new UsernamePasswordAuthenticationToken(
                                        admin.getUsername(),
                                        null,
                                        Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + admin.getRole().toUpperCase()))
                                    );
                                
                                SecurityContextHolder.getContext().setAuthentication(authentication);
                                log.debug("用户[{}]认证成功", username);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("JWT认证失败", e);
            }
        }
        
        filterChain.doFilter(request, response);
    }
}
