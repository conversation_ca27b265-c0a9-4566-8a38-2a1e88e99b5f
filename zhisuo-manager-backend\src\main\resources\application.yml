server:
  port: 8081
  servlet:
    context-path: /manager

spring:
  application:
    name: zhisuo-manager-backend
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************
    username: root
    password: root
    
  redis:
    host: localhost
    port: 6379
    password: gcf021206
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: zhisuo-manager-secret-key-2025-abcdef1234567890abcdef1234567890abcdef1234567890
  expiration: 86400000  # 24小时
  header: Authorization
  prefix: Bearer

# 应用启动配置
app:
  # 端口占用处理配置
  port-check:
    # 是否自动关闭占用端口的进程
    auto-kill-processes: true
    # 端口占用时是否退出应用
    exit-on-occupied: false
    # 进程关闭后等待时间(毫秒)
    wait-after-kill: 2000

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-cn-guangzhou.aliyuncs.com
    access-key-id: LTAI5tPzwQpzrzu6w5kBzfSm
    access-key-secret: ******************************
    bucket-name: fcg02
    use-https: true
    connection-timeout: 10000
    socket-timeout: 10000
    max-connections: 1024

# 日志配置
logging:
  level:
    com.zhisuo.manager: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
