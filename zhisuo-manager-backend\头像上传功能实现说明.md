# 头像上传功能实现说明

## 功能概述

已成功为智索管理后端实现了完整的头像上传功能，支持将头像文件上传到阿里云OSS存储。

## 实现的组件

### 1. 依赖配置
- **pom.xml** - 添加了阿里云OSS SDK依赖
  - `aliyun-sdk-oss: 3.16.1`
  - `commons-io: 2.11.0`

### 2. 配置类
- **OssConfig.java** - 阿里云OSS配置类
  - 支持配置端点、访问密钥、存储桶等
  - 提供URL生成方法
  - 支持自定义域名

### 3. 工具类
- **OssUtil.java** - OSS操作工具类
  - 文件上传功能
  - 文件删除功能
  - 文件存在性检查
  - 文件类型验证
  - 文件大小验证

### 4. 控制器
- **UploadController.java** - 文件上传控制器
  - `/upload/avatar` - 头像上传接口
  - `/upload/file` - 通用文件上传接口
  - `/upload/delete` - 文件删除接口

### 5. 配置文件
- **application.yml** - 添加了OSS配置和文件上传配置

## API接口详情

### 头像上传接口

**接口地址**: `POST /manager/upload/avatar`

**请求参数**:
- `file`: MultipartFile - 头像文件

**请求头**:
- `Authorization`: Bearer {token} - JWT认证令牌

**文件限制**:
- 文件类型: 仅支持图片格式 (jpg, jpeg, png, gif, bmp, webp, svg)
- 文件大小: 最大2MB
- 存储路径: `image/avatar/` 目录

**响应格式**:
```json
{
  "code": 200,
  "message": "头像上传成功",
  "data": {
    "url": "https://fcg02.oss-cn-guangzhou.aliyuncs.com/image/avatar/20250804183311_abc123def456.jpg",
    "originalName": "avatar.jpg",
    "size": "102400"
  },
  "timestamp": 1704355200000
}
```

### 通用文件上传接口

**接口地址**: `POST /manager/upload/file`

**请求参数**:
- `file`: MultipartFile - 文件
- `type`: String - 文件类型 (可选，默认为"file")

**文件类型分类**:
- `avatar` → `image/avatar/`
- `image` → `image/`
- `document` → `document/`
- `video` → `video/`
- `audio` → `audio/`
- `file` → `file/` (默认)

**文件限制**:
- 文件大小: 最大10MB

### 文件删除接口

**接口地址**: `DELETE /manager/upload/delete`

**请求参数**:
```json
{
  "fileUrl": "https://fcg02.oss-cn-guangzhou.aliyuncs.com/image/avatar/filename.jpg"
}
```

**权限要求**: 仅管理员可删除文件

## 配置说明

### OSS配置 (application.yml)
```yaml
aliyun:
  oss:
    endpoint: oss-cn-guangzhou.aliyuncs.com
    access-key-id: LTAI5tPzwQpzrzu6w5kBzfSm
    access-key-secret: ******************************
    bucket-name: fcg02
    use-https: true
    connection-timeout: 10000
    socket-timeout: 10000
    max-connections: 1024
```

### 文件上传配置
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
```

## 安全特性

1. **权限控制**: 使用Spring Security注解控制访问权限
2. **文件类型验证**: 严格验证上传文件的MIME类型
3. **文件大小限制**: 防止大文件上传影响系统性能
4. **唯一文件名**: 使用时间戳+UUID生成唯一文件名，防止文件名冲突
5. **错误处理**: 完善的异常处理和日志记录

## 测试方法

### 1. 使用Postman测试

**步骤**:
1. 启动管理后端: `mvn spring-boot:run`
2. 获取JWT令牌（通过登录接口）
3. 设置请求:
   - URL: `http://localhost:8081/manager/upload/avatar`
   - Method: POST
   - Headers: `Authorization: Bearer {token}`
   - Body: form-data, key="file", 选择图片文件

### 2. 使用前端测试

前端已经配置好了上传API，可以直接在个人信息页面测试头像上传功能。

### 3. 验证上传结果

上传成功后，可以通过返回的URL直接访问图片，验证是否正确上传到阿里云OSS。

## 文件命名规则

上传的文件会按照以下规则重命名：
- 格式: `{时间戳}_{UUID}.{扩展名}`
- 示例: `20250804183311_abc123def456789.jpg`

这样可以确保：
1. 文件名唯一性
2. 按时间排序
3. 保留原始文件扩展名

## 错误处理

常见错误及处理：

1. **文件为空**: "上传文件不能为空"
2. **文件类型错误**: "上传文件类型错误，仅支持图片格式"
3. **文件过大**: "上传图片大小不能超过2MB"
4. **上传失败**: "上传头像失败: {具体错误信息}"
5. **权限不足**: 401 Unauthorized

## 日志记录

系统会记录以下日志：
- 文件上传成功日志
- 文件上传失败日志
- 文件删除操作日志
- 异常错误日志

## 性能优化

1. **连接池**: 配置了OSS客户端连接池
2. **超时设置**: 合理的连接和读取超时时间
3. **资源释放**: 及时关闭OSS客户端连接
4. **缓存控制**: 设置文件缓存策略

## 扩展功能

当前实现支持以下扩展：
1. **自定义域名**: 支持配置CDN域名
2. **多文件上传**: 可扩展支持批量上传
3. **图片处理**: 可集成阿里云图片处理服务
4. **文件管理**: 可扩展文件列表、搜索等功能

## 注意事项

1. **密钥安全**: 生产环境应使用环境变量或配置中心管理密钥
2. **权限控制**: 根据实际需求调整接口权限
3. **存储成本**: 定期清理无用文件，控制存储成本
4. **备份策略**: 重要文件建议配置跨区域备份

## 服务状态

- **管理后端**: ✅ 已启动 (http://localhost:8081/manager)
- **前端服务**: ✅ 已启动 (http://localhost:3001)
- **OSS配置**: ✅ 已配置
- **接口测试**: ✅ 可以开始测试
