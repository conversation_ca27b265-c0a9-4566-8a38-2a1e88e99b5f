import request from '@/utils/request'

export const authApi = {
  // 登录
  login(data) {
    return request({
      url: '/auth/login',
      method: 'post',
      data
    })
  },

  // 登出
  logout() {
    return request({
      url: '/auth/logout',
      method: 'post'
    })
  },

  // 获取当前用户信息
  getCurrentUser() {
    return request({
      url: '/auth/current',
      method: 'get'
    })
  },

  // 更新用户资料
  updateProfile(data) {
    return request({
      url: '/auth/profile',
      method: 'put',
      data
    })
  },

  // 修改密码
  changePassword(data) {
    return request({
      url: '/auth/password',
      method: 'put',
      data
    })
  },

  // 获取用户统计数据
  getUserStats() {
    return request({
      url: '/auth/stats',
      method: 'get'
    })
  }
}
