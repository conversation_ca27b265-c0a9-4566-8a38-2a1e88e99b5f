import request from '@/utils/request'

export const uploadApi = {
  /**
   * 上传头像
   * @param {File} file 头像文件
   * @returns {Promise} 上传结果
   */
  uploadAvatar(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return request({
      url: '/upload/avatar',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 30000 // 30秒超时，因为文件上传可能需要更长时间
    })
  },

  /**
   * 上传文件（通用）
   * @param {File} file 文件
   * @param {string} type 文件类型 (avatar, document, image等)
   * @returns {Promise} 上传结果
   */
  uploadFile(file, type = 'file') {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    
    return request({
      url: '/upload/file',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000 // 60秒超时
    })
  },

  /**
   * 批量上传文件
   * @param {FileList|Array} files 文件列表
   * @param {string} type 文件类型
   * @returns {Promise} 上传结果
   */
  uploadMultipleFiles(files, type = 'file') {
    const formData = new FormData()
    
    // 添加多个文件
    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i])
    }
    formData.append('type', type)
    
    return request({
      url: '/upload/multiple',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000 // 2分钟超时
    })
  },

  /**
   * 删除文件
   * @param {string} fileUrl 文件URL
   * @returns {Promise} 删除结果
   */
  deleteFile(fileUrl) {
    return request({
      url: '/upload/delete',
      method: 'delete',
      data: { fileUrl }
    })
  },

  /**
   * 获取文件上传进度（如果后端支持）
   * @param {string} uploadId 上传ID
   * @returns {Promise} 进度信息
   */
  getUploadProgress(uploadId) {
    return request({
      url: `/upload/progress/${uploadId}`,
      method: 'get'
    })
  }
}

/**
 * 文件上传工具函数
 */
export const uploadUtils = {
  /**
   * 验证文件类型
   * @param {File} file 文件对象
   * @param {Array} allowedTypes 允许的文件类型数组
   * @returns {boolean} 是否通过验证
   */
  validateFileType(file, allowedTypes = ['image/jpeg', 'image/png', 'image/gif']) {
    return allowedTypes.includes(file.type)
  },

  /**
   * 验证文件大小
   * @param {File} file 文件对象
   * @param {number} maxSize 最大文件大小（字节）
   * @returns {boolean} 是否通过验证
   */
  validateFileSize(file, maxSize = 2 * 1024 * 1024) { // 默认2MB
    return file.size <= maxSize
  },

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * 获取文件扩展名
   * @param {string} filename 文件名
   * @returns {string} 文件扩展名
   */
  getFileExtension(filename) {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
  },

  /**
   * 生成文件预览URL
   * @param {File} file 文件对象
   * @returns {string} 预览URL
   */
  createPreviewUrl(file) {
    return URL.createObjectURL(file)
  },

  /**
   * 释放文件预览URL
   * @param {string} url 预览URL
   */
  revokePreviewUrl(url) {
    URL.revokeObjectURL(url)
  },

  /**
   * 压缩图片
   * @param {File} file 图片文件
   * @param {number} quality 压缩质量 (0-1)
   * @param {number} maxWidth 最大宽度
   * @param {number} maxHeight 最大高度
   * @returns {Promise<File>} 压缩后的文件
   */
  compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        // 计算新的尺寸
        let { width, height } = img
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
        
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
        
        canvas.width = width
        canvas.height = height
        
        // 绘制图片
        ctx.drawImage(img, 0, 0, width, height)
        
        // 转换为Blob
        canvas.toBlob(
          (blob) => {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          },
          file.type,
          quality
        )
      }
      
      img.onerror = reject
      img.src = URL.createObjectURL(file)
    })
  }
}

export default uploadApi
